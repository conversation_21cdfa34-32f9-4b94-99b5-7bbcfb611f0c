<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkillTemplate;
use App\Models\MobSkill;
use App\Services\LogFormattingService;
use App\Services\BattleLogService;

class TestMineStunSkillLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:mine-stun-log {user_id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Тестирует отображение скилла стана в журнале боя рудников';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');

        // Получаем пользователя или создаем тестового
        $user = User::find($userId);
        if (!$user) {
            // Попробуем найти любого пользователя
            $user = User::first();
            if (!$user) {
                $this->error("Пользователи не найдены в системе!");
                return 1;
            }
            $this->info("Используем пользователя: {$user->name} (ID: {$user->id})");
        }

        $this->info("🧪 Тестируем отображение скилла стана для пользователя: {$user->name}");

        // Находим или создаем моба
        $mob = Mob::where('name', 'Огр')->first();
        if (!$mob) {
            $this->error("Моб 'Огр' не найден!");
            return 1;
        }

        // Находим шаблон скилла стана
        $stunTemplate = MobSkillTemplate::where('name', 'Тяжелый удар')
            ->where('effect_type', 'stun')
            ->first();

        if (!$stunTemplate) {
            $this->error("Шаблон скилла 'Тяжелый удар' не найден!");
            return 1;
        }

        $this->info("✅ Найден шаблон скилла: {$stunTemplate->name}");

        // Создаем сервисы
        $logFormatter = app(LogFormattingService::class);
        $battleLogService = app(BattleLogService::class);

        // Тестируем обычную атаку без скиллов
        $this->info("\n📝 Тестируем обычную атаку без скиллов:");
        $normalAttackMessage = $logFormatter->formatMineDetectionAttack(
            $mob->name,
            $user->name,
            25,
            'Тестовый рудник',
            []
        );
        $this->line("Результат: " . strip_tags($normalAttackMessage));

        // Тестируем атаку со скиллом стана
        $this->info("\n⚡ Тестируем атаку со скиллом стана:");
        $skillIcon = $stunTemplate->icon && file_exists(public_path($stunTemplate->icon))
            ? "<img src='" . asset($stunTemplate->icon) . "' alt='Тяжелый удар' class='w-4 h-4 inline-block align-middle'>"
            : "⚡"; // Иконка по умолчанию для стана

        $activatedSkills = [
            [
                'success' => true,
                'skill_type' => 'stun',
                'duration' => 5,
                'message' => "оглушает Вас на {$skillIcon} 5 секунд!"
            ]
        ];

        $stunAttackMessage = $logFormatter->formatMineDetectionAttack(
            $mob->name,
            $user->name,
            25, // Урон не должен отображаться
            'Тестовый рудник',
            $activatedSkills
        );
        $this->line("Результат: " . strip_tags($stunAttackMessage));
        $this->line("HTML версия: " . $stunAttackMessage);

        // Добавляем тестовые логи в журнал боя пользователя
        $battleLogKey = "battle_logs:mines:{$userId}";

        $this->info("\n📋 Добавляем тестовые логи в журнал боя...");
        $battleLogService->addLog($battleLogKey, $normalAttackMessage, 'danger');
        $battleLogService->addLog($battleLogKey, $stunAttackMessage, 'warning');

        $this->info("✅ Тестовые логи добавлены в журнал боя пользователя {$userId}");

        // Тестируем также обычный formatMobAttack
        $this->info("\n🔄 Тестируем formatMobAttack со скиллом стана:");
        $mobAttackMessage = $logFormatter->formatMobAttack(
            $mob,
            $user,
            25,
            false,
            $activatedSkills
        );
        $this->line("Результат: " . strip_tags($mobAttackMessage));

        $battleLogService->addLog($battleLogKey, $mobAttackMessage, 'warning');

        $this->info("\n🎉 Тест завершен! Проверьте журнал боя в игре.");
        $this->info("💡 Для просмотра логов используйте ключ: {$battleLogKey}");

        return 0;
    }
}
